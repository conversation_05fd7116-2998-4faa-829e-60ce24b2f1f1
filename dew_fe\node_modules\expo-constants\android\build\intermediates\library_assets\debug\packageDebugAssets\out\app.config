{"name": "ai_therapist", "slug": "ai_therapist", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "aitherapist", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.javedakeeb.ai_therapist"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": [["expo-font", {"fonts": ["./assets/fonts/HankenGrotesk-Regular.ttf", "./assets/fonts/HankenGrotesk-Bold.ttf"]}], ["@react-native-google-signin/google-signin"], ["expo-secure-store", {"configureAndroidBackup": true, "faceIDPermission": "Allow $(AI_THERAPIST) to access your Face ID biometric data."}], "expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-font", "expo-web-browser", "expo-secure-store"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "2cd537d4-659f-47e7-a1e5-24daba8d4476"}}, "sdkVersion": "53.0.0", "platforms": ["ios", "android", "web"], "androidStatusBar": {"backgroundColor": "#ffffff"}}