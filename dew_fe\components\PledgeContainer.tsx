import { <PERSON><PERSON>, <PERSON><PERSON>, Text, View } from "react-native";
import Colors from "../constants/Colors";
import { supabase } from "../lib/supabase";
import * as SecureStore from "expo-secure-store";
import { useEffect, useState } from "react";

export default function PledgeContainer() {
  const [userId, setUserId] = useState<string | null>(null);
  const [isPledgeTaken, setIsPledgeTaken] = useState<boolean | null>(false);

  console.log("isPledgeTaken", isPledgeTaken);

  const takePledgeHandler = async () => {
    try {
      const today = new Date().toISOString().split("T")[0]; // Get today's date in YYYY-MM-DD format

      const { data, error } = await supabase.from("pledges").insert({
        pledge_date: today,
        is_pledged: true,
        user_id: userId,
      }).select();

      console.log("response", data);
      if (error) {
        console.error("Error saving pledge:", error);
        Alert.alert("Error", "Failed to save your pledge. Please try again.");
      } else {
        // Update local state to reflect the pledge was taken
        setIsPledgeTaken(true);
        Alert.alert("Success", "Your pledge has been recorded!");
      }
    } catch (err) {
      console.error("Unexpected error:", err);
      Alert.alert("Error", "Something went wrong. Please try again.");
    }
  };

  const getPledge = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from("pledges")
        .select("*")
        .eq("user_id", userId)
        .single();
      console.log("data22", data?.is_pledged);
      setIsPledgeTaken(data?.is_pledged); // set isPledgeTaken state to the pledge_text
    } catch (error) {
      console.error("Error fetching pledge:", error);
      Alert.alert("Error", "Failed to fetch your pledge. Please try again.");
    }
  };

  useEffect(() => {
    getPledge(userId || "");
  }, [isPledgeTaken]);

  async function getSecureItem(key: string) {
    // get user_id from secure store
    const value = await SecureStore.getItemAsync(key);
    setUserId(value);
    return value;
  }

  useEffect(() => {
    getSecureItem("user_id"); // get user_id from secure store and set it to userId state
  }, []);

  return (
    <View
      className="rounded-2xl p-5 shadow-lg shadow-black/50"
      style={{
        backgroundColor: Colors.background.elevated,
        borderWidth: 1,
        borderColor: "rgba(255,255,255,0.03)",
      }}
    >
      <View className="flex-row justify-between items-center">
        <View className="flex-row items-center">
          <View className="w-12 h-12 rounded-full bg-amber-900/30 items-center justify-center mr-3">
            <Text className="text-2xl">🤝</Text>
          </View>
          <View>
            <Button
              title={isPledgeTaken === true ? "✓ Pledged Today!" : "Take Daily Pledge"}
              onPress={isPledgeTaken ? undefined : takePledgeHandler}
              disabled={isPledgeTaken === true}
              color={isPledgeTaken === true ? Colors.status.info : Colors.status.error}
            />
            <Text style={{ color: Colors.text.secondary }} className="text-sm">
              {isPledgeTaken === true
                ? "You've committed to today's journey"
                : "Reinforce your commitment"}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
}
