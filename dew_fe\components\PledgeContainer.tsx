import { Alert, Pressable, Text, View } from "react-native";
import { supabase } from "../lib/supabase";
import * as SecureStore from "expo-secure-store";
import { useEffect, useState } from "react";

export default function PledgeContainer() {
  const [userId, setUserId] = useState<string | null>(null);
  const [isPledgeTaken, setIsPledgeTaken] = useState<boolean | null>(false);

  console.log("isPledgeTaken", isPledgeTaken);

  const takePledgeHandler = async () => {
    try {
      const today = new Date().toISOString().split("T")[0]; // Get today's date in YYYY-MM-DD format

      const { data, error } = await supabase.from("pledges").insert({
        pledge_date: today,
        is_pledged: true,
        user_id: userId,
      }).select();

      console.log("response", data);
      if (error) {
        console.error("Error saving pledge:", error);
        Alert.alert("Error", "Failed to save your pledge. Please try again.");
      } else {
        // Update local state to reflect the pledge was taken
        setIsPledgeTaken(true);
        Alert.alert("Success", "Your pledge has been recorded!");
      }
    } catch (err) {
      console.error("Unexpected error:", err);
      Alert.alert("Error", "Something went wrong. Please try again.");
    }
  };

  const getPledge = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from("pledges")
        .select("*")
        .eq("user_id", userId)
        .single();
      console.log("data22", data?.is_pledged);
      setIsPledgeTaken(data?.is_pledged); // set isPledgeTaken state to the pledge_text
    } catch (error) {
      console.error("Error fetching pledge:", error);
      Alert.alert("Error", "Failed to fetch your pledge. Please try again.");
    }
  };

  useEffect(() => {
    getPledge(userId || "");
  }, [isPledgeTaken]);

  async function getSecureItem(key: string) {
    // get user_id from secure store
    const value = await SecureStore.getItemAsync(key);
    setUserId(value);
    return value;
  }

  useEffect(() => {
    getSecureItem("user_id"); // get user_id from secure store and set it to userId state
  }, []);

  return (
    <View className="bg-gray-900/95 rounded-3xl p-6 mx-4 my-3 border border-gray-700/50 shadow-2xl">
      {/* Header Section */}
      <View className="flex-row items-center mb-6">
        <View className="w-14 h-14 rounded-full bg-gradient-to-br from-amber-500 to-orange-600 items-center justify-center mr-4 shadow-lg">
          <Text className="text-3xl">🤝</Text>
        </View>
        <View className="flex-1">
          <Text className="text-white text-xl font-bold mb-1">Daily Pledge</Text>
          <Text className="text-gray-400 text-sm">
            {isPledgeTaken === true
              ? "Commitment completed for today"
              : "Strengthen your resolve"}
          </Text>
        </View>
      </View>

      {/* Pledge Content */}
      <View className="mb-6">
        <Text className="text-gray-300 text-base leading-6 italic text-center px-2">
          &ldquo;I pledge to stay committed to my journey today, taking it one day at a time.
          Every moment of resistance makes me stronger.&rdquo;
        </Text>
      </View>

      {/* Action Button */}
      <View className="items-center">
        <Pressable
          onPress={isPledgeTaken ? undefined : takePledgeHandler}
          disabled={isPledgeTaken === true}
          className={`w-full py-4 px-6 rounded-2xl items-center justify-center shadow-lg ${
            isPledgeTaken === true
              ? 'bg-green-600/80 border border-green-500/50'
              : 'bg-gradient-to-r from-blue-600 to-purple-600 border border-blue-500/50 active:scale-95'
          } ${isPledgeTaken === true ? 'opacity-80' : 'opacity-100'}`}
          style={{
            shadowColor: isPledgeTaken === true ? '#10b981' : '#3b82f6',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 8,
            elevation: 8,
          }}
        >
          <View className="flex-row items-center">
            {isPledgeTaken === true && (
              <Text className="text-white text-lg mr-2">✓</Text>
            )}
            <Text className="text-white text-lg font-bold tracking-wide">
              {isPledgeTaken === true ? "Pledged Today!" : "Take Today's Pledge"}
            </Text>
          </View>
        </Pressable>
      </View>

      {/* Status Indicator */}
      <View className="mt-4 items-center">
        <View className={`px-4 py-2 rounded-full ${
          isPledgeTaken === true
            ? 'bg-green-900/50 border border-green-600/30'
            : 'bg-blue-900/50 border border-blue-600/30'
        }`}>
          <Text className={`text-sm font-medium ${
            isPledgeTaken === true ? 'text-green-300' : 'text-blue-300'
          }`}>
            {isPledgeTaken === true
              ? "🎉 Today's commitment secured"
              : "💪 Ready to commit?"}
          </Text>
        </View>
      </View>
    </View>
  );
}
