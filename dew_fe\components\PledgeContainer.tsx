import { Alert, Pressable, Text, View } from "react-native";
import Colors from "../constants/Colors";
import { supabase } from "../lib/supabase";
import * as SecureStore from "expo-secure-store";
import { useEffect, useState } from "react";

export default function PledgeContainer() {
  const [userId, setUserId] = useState<string | null>(null);
  const [isPledgeTaken, setIsPledgeTaken] = useState<boolean | null>(null);

  console.log("isPledgeTaken", isPledgeTaken);

  const takePledgeHandler = async () => {
    try {
      const today = new Date().toISOString().split("T")[0]; // Get today's date in YYYY-MM-DD format

      const response = await supabase.from("pledges").insert({
        pledge_date: today,
        pledge_text: "I pledge to stay committed to my journey today, taking it one day at a time. Every moment of resistance makes me stronger.",
        user_id: userId,
      });

      if (error) {
        console.error("Error saving pledge:", error);
        Alert.alert("Error", "Failed to save your pledge. Please try again.");
      } else {
        // Update local state to reflect the pledge was taken
        setIsPledgeTaken(true);
        Alert.alert("Success", "Your pledge has been recorded!");
      }
    } catch (err) {
      console.error("Unexpected error:", err);
      Alert.alert("Error", "Something went wrong. Please try again.");
    }
  };

  const getPledge = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from("pledges")
        .select("*")
        .eq("user_id", userId)
        .single();
      console.log("data", data);
      setIsPledgeTaken(data?.pledge_text); // set isPledgeTaken state to the pledge_text
    } catch (error) {
      console.error("Error fetching pledge:", error);
      Alert.alert("Error", "Failed to fetch your pledge. Please try again.");
    }
  };

  useEffect(() => {
    getPledge(userId || "");
  }, [userId]);

  async function getSecureItem(key: string) {
    // get user_id from secure store
    const value = await SecureStore.getItemAsync(key);
    setUserId(value);
    return value;
  }

  useEffect(() => {
    getSecureItem("user_id"); // get user_id from secure store and set it to userId state
  }, []);

  return (
    <View
      className="rounded-2xl p-5 shadow-lg shadow-black/50"
      style={{
        backgroundColor: Colors.background.elevated,
        borderWidth: 1,
        borderColor: "rgba(255,255,255,0.03)",
      }}
    >
      <View className="flex-row justify-between items-center">
        <View className="flex-row items-center">
          <View className="w-12 h-12 rounded-full bg-amber-900/30 items-center justify-center mr-3">
            <Text className="text-2xl">🤝</Text>
          </View>
          <View>
            <Pressable
              onPress={takePledgeHandler}
              className={`my-4 px-8 py-4 rounded-xl items-center justify-center shadow-lg`}
              style={{
                backgroundColor: isPledgeTaken
                  ? Colors.status.success
                  : Colors.brand.primary,
                elevation: 8,
                shadowColor: isPledgeTaken
                  ? Colors.status.success
                  : Colors.brand.primary,
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 8,
              }}
            >
              <Text
                style={{
                  color: Colors.text.primary,
                  fontSize: 22,
                  letterSpacing: 0.5,
                }}
                className="font-bold font-hanken-grotesk"
              >
                {isPledgeTaken ? "✓ Pledged Today!" : "Take Daily Pledge"}
              </Text>
            </Pressable>
            <Text style={{ color: Colors.text.secondary }} className="text-sm">
              {isPledgeTaken
                ? "You've committed to today's journey"
                : "Reinforce your commitment"}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
}
